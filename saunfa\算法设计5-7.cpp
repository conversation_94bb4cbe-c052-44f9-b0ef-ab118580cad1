#include <iostream>
#include <vector>
#include <cmath>
#include <limits>

using namespace std;

// 计算两个点之间的欧几里得距离
double euclideanDistance(const vector<double>& p1, const vector<double>& p2) {
    double sum = 0.0;
    for (size_t i = 0; i < p1.size(); ++i) {
        sum += (p1[i] - p2[i]) * (p1[i] - p2[i]);
    }
    return sqrt(sum);
}

// 蛮力算法求解k维空间最近的点对
void closestPairBruteForce(const vector<vector<double>>& points) {
    int n = points.size();
    double minDistance = numeric_limits<double>::max();
    vector<int> closestPair;

    for (int i = 0; i < n; ++i) {
        for (int j = i + 1; j < n; ++j) {
            double distance = euclideanDistance(points[i], points[j]);
            if (distance < minDistance) {
                minDistance = distance;
                closestPair = {i, j};
            }
        }
    }

    cout << "最近的点对是：" << closestPair[0] << " 和 " << closestPair[1] << endl;
    cout << "距离是：" << minDistance << endl;
}

int main() {
    // 示例点集
    vector<vector<double>> points = {
        {1, 2},
        {3, 4},
        {5, 6},
        {7, 8},
        {9, 10}
    };

    closestPairBruteForce(points);

    return 0;
}
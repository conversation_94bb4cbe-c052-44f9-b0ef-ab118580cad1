#include <iostream>
#include <vector>
using namespace std;

int main(){
    //初始化
    vector<int> ret ={1,2,3,4,5};
    //大小获取
    for(int i=0; i<ret.size(); ++i){
        cout<<ret[i]<<" ";
    }
    cout<<endl;
    cout<<ret.size()<<endl;
    //元素插入
    ret.push_back(1024);
    for(int i=0; i<ret.size(); ++i){
        cout<<ret[i]<<" ";
    }
    cout<<endl;
    cout<<ret.size()<<endl;
    cout<<ret[0]<<endl;
    return 0;
}
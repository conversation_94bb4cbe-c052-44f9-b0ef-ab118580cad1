'''
定义一个整型数组，并将指定个数的元素翻转到数组的尾部。
例如：(ar[], d, n) 将长度为 n 的 数组 arr 的前面 d 个元素翻转到数组尾部。
以下演示了将数组的前面两个元素放到数组后面。
原始数组:  1234567
翻转后：   3456712
'''
def leftRotate(a,d,n):
    for i in range(d):
        leftRotatebyOne(a,n)
def leftRotatebyOne(a,n):
    temp =a[0]
    for i in range(n-1):
        a[i] =a[i+1]
    a[n-1] =temp

def printArray(a,size):
    for i in range(size):
        print('%d'% a[i],end=" ")

a=[1,2,3,4,5,6,7]
leftRotate(a,5,7)
printArray(a,7)
    
#翻转列表
def Reverse(lst): 
    lst.reverse() 
    return lst 
      
lst = [10, 11, 12, 13, 14, 15] 
print(Reverse(lst))
#include <iostream>
using namespace std;

class Hero{
    public:
    Hero() : m_Data(NULL){}
    Hero(int data){
        m_Data = new int;
        *m_Data = data;
    }
    int* m_Data;
    //double delete
    //析构函数
    ~Hero(){
        if(m_Data){
            delete m_Data;
            m_Data = NULL;
        }
    }

    void operator=(Hero& h){
        //m_Data = h.m_Data;
        if(m_Data){
            delete m_Data;
            m_Data = NULL;

        }
        m_Data =new int;
        *m_Data = *h.m_Data;
    }
    int* m_Data;
};

int main(){
    Hero h1(1);
    Hero h2(2);

    cout<<h1.m_Data<<endl;
    cout<<h2.m_Data<<endl;
    h1 = h2; //内存泄漏
    cout<<h1.m_Data<<endl;
    cout<<h2.m_Data<<endl;

    return 0;
}
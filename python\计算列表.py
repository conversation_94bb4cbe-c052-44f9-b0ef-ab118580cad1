'''定义一个列表，并计算某个元素在列表中出现的次数。
   输入 : lst = [15, 6, 7, 10, 12, 20, 10, 28, 10]
       x = 10
   输出 : 3 
'''
def countX(lst,x):
    count = 0
    for i in lst:
        if(i == x):
            count =count +1
    return count
lst = [15, 6, 7, 10, 12, 20, 10, 28, 10]
x = 10
print(countX(lst,x))

#使用count()方法
def countR(lst, x): 
    return lst.count(x) 
  
lst = [8, 6, 8, 10, 8, 20, 10, 8, 8] 
x = 8
print(countR(lst, x))


#计算列表元素之和
'''定义一个数字列表，并计算列表元素之和。

   例如： 输入 : [12, 15, 3, 10] 输出 : 40
'''
total =0
list1 =[12,15,3,10]
for i in range(0,len(list1)):
    total = total + list1[i]
    print("每次列表元素之和为: ", total)
print("列表元素之和为: ", total)

#使用while()循环
total = 0
ele = 0
  
list1 = [11, 5, 17, 18, 23]  
  
while(ele < len(list1)): 
    total = total + list1[ele] 
    ele += 1
      
print("列表元素之和为: ", total)

list1 = [11, 5, 17, 18, 23] 

#递归
def sumOfList(list, size): 
   if (size == 0): 
     return 0
   else: 
     return list[size - 1] + sumOfList(list, size - 1) 
      
total = sumOfList(list1, len(list1)) 

print("列表元素之和为: ", total)
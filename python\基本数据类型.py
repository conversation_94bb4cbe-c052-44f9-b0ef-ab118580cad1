counter =100      #整形变量
miles   =1000.0   #浮点型变量
name    ='runoob' #字符串

print(counter)
print(miles)
print(name)

#多个变量赋值
a = b = c = 1

a,b,c = 1,2,'runoob'

print(a)
print(b)
print(c)

sites = {'Google','Taobao','runoob','Facebook','Zhihu','Baidu'}
print(sites)

#成员测试
if 'Runoob' in sites :
    print('Runoob在集合中')
else:
    print('Runoob不在集合中')


# set可以进行集合运算
a = set('abracadabra')
b = set('alacazam')

print(a)
print(a -b)
print(a |b)
print(a &b)
print(a ^b)

dict ={}
dict['one'] = "1-菜鸟教程"
dict[2] = "2-菜鸟工具"

tinydict = {'name':'runoob','code':1,'site':'www.runoob.com'}

print(dict['one'])
print(dict[2])
print(tinydict)
print(tinydict.keys())
print(tinydict.values())
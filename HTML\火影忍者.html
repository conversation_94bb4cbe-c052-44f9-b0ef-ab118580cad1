<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>火影忍者游戏启动界面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 548px;
            width: 1062px;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin: auto;
        }
        
        .container {
            text-align: center;
            background-color: rgba(255, 255, 255, 0.8);
            background-image: url('img/背景图.jpg.png');
            background-size: cover;
            /* 等比例缩放背景图片，直到完全覆盖容器 */
            background-position: center;
            /* 将背景图片居中显示 */
            padding: 100px;
            border-radius: 50px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 1064px;
            margin: auto;
            /* 使容器在父容器中居中显示 */
        }
        
        .title {
            font-size: 24px;
            color: #333;
        }
        
        .options {
            margin: 20px 0;
        }
        
        .option {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            color: white;
            background-color: rgba(249, 249, 249, 0.5);
            /* 半透明背景 */
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 16px;
        }
        
        .option:hover {
            background-color: rgba(255, 255, 255, 0.75);
            /* 鼠标悬停时更不透明 */
        }
        
        .option img {
            width: 24px;
            /* Adjust the size as needed */
            height: 24px;
            /* Adjust the size as needed */
            vertical-align: middle;
            margin-right: 8px;
        }
        
        .agreement {
            font-size: 14px;
            color: #000000;
            margin-top: 20px;
        }
        
        .agreement input {
            margin-right: 5px;
        }
        
        .footer {
            margin-top: 20px;
            font-size: 12px;
            color: #000000;
        }
    </style>
    <script>
        function openWeChat() {
            alert("打开微信窗口");
            // 这里可以添加打开微信的代码，例如使用window.open打开微信网页版
        }

        function openQQ() {
            alert("打开QQ窗口");
            // 这里可以添加打开QQ的代码，例如使用window.open打开QQ网页版
        }
    </script>
</head>

<body>
    <div class="container">
        <div class="title">NARUTO 火影忍者</div>
        <div class="options">
            <a href="https://weixin.qq.com/" class="option">
                <img src="img/123.jpg.png" alt="WeChat">与微信好友玩
            </a>
            <a href="https://im.qq.com/" class="option">
                <img src="img/qq图标.png" alt="QQ">与QQ好友玩
            </a>
            <!-- 新增的注册游戏账号按钮 -->
            <a href="file:///D:/VScodeprojects/day01/HTML/%E7%81%AB%E5%BD%B1%E5%BF%8D%E8%80%85%E7%95%8C%E9%9D%A2.html" class="option" id="registerOption">
                <alt="Register">注册游戏账号

        </div>
        <div class="agreement">
            <input type="checkbox" id="agree">
            <label for="agree">我已详细阅读并同意游戏许可及服务协议，游戏隐私保护指引，游戏儿童隐私保护指引和第三方信息共享清单。</label>
        </div>
        <div class="footer">

        </div>
    </div>
</body>

</html>
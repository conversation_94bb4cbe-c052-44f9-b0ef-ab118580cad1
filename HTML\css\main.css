     * {
         margin: 0px;
         padding: 0px;
         
     }
     
     div,
     nav,
     dl,
     dt,
     dd #lb,
     #product {
         border: 1px solid rgb(231, 9, 9);
     }
     
     a {
         text-decoration: none;
     }
     
     header {
         /*background: url("../img/logo.jpg");
    height: 100px;
    background-repeat: no-repeat;
    background-position: center;
    /background-position: center top;
    background-size: 100%;
    background-attachment: fixed;
    */
         text-align: center;
         margin: 15px 0px;
     }
     
     nav {
         height: 44px;
         background: url("../img/导航栏背景.jpg");
     }
     
     nav dl {
         display: flex;
         justify-content: center;
     }
     
     nav dl dt {
         width: 120px;
         font-family: "黑体";
         color: #fff;
         text-align: center;
         letter-spacing: 5px;
         height: 32px;
         padding-top: 9px;
         position: relative;
     }
     
     nav dl dt:hover {
         background: url("../img/导航栏4.gif");
     }
     
     nav dl dt dl {
         flex-direction: column;
         color: #fffdfd;
         margin-top: 14px;
         background: url("../img/下拉菜单背景图.png");
         display: none;
         position: absolute;
         z-index: 10;
         z-index: 10;
         left: 0;
         right: 0;
         width: 100%;
     }
     
     nav dl dt dl dd {
         padding: 10px 0px;
         border-bottom: #fff 2px solid;
     }
     
     nav dl dt dl :hover {
         background: url("../img/导航栏4.gif");
     }
     /* 鼠标悬停时显示下拉菜单 */
     
     nav dl dt dl {
         display: block;
         max-height: 0;
         overflow: hidden;
         transition: max-height 0.5s ease-in-out;
     }
     
     nav dl dt:hover dl {
         max-height: 300px;
         /* 根据实际内容调整高度 */
     }
     
     #lb {
         width: 990px;
         height: 343px;
         margin: 10px auto;
         display: flex;
         justify-content: space-between;
     }
     
     #lb_left {
         width: 720px;
         height: 343px;
         /*position: absolute;*/
     }
     
     #lb_right {
         width: 247px;
         height: 290px;
     }
     
     #lb_right dl dd {
         height: 17px;
         font-size: 12px;
         color: #666;
         overflow: hidden;
         margin-top: 5px;
     }
     
     #lb marquee {
         position: absolute;
         font-size: 16px;
         color: #fff;
         z-index: 11;
     }
     
     #product {
         width: 990px;
         height: auto;
         margin: 0px auto;
         background: url(../img/猜你喜欢.jpg);
         background-repeat: no-repeat;
         background-position: center top;
         padding-top: 50px;
     }
     
     #product dl {
         display: flex;
         flex-wrap: wrap;
         justify-content: space-between;
     }
     
     #product dl dt {
         width: 190px;
         height: 260px;
         /* border: 1px solid red; */
         overflow: hidden;
         border: 1px solid red(255, 255, 255);
     }
     
     #product dl dt h4 {
         font-size: 12px;
         color: rgb(12, 11, 11);
         font-weight: normal;
         padding: 4px 10px;
         height: 30px;
         overflow: hidden;
     }
     
     #product dl dt h3 {
         font-size: 13px;
         color: rgb(0, 0, 0);
         margin: 2px 10px;
     }
     
     #product dl dt:hover {
         border: 1px solid red(250, 75, 0);
         transform: scale(1.1);
         background-color: #fff;
         transition: tansform 0.2s;
     }
     
     footer {
         background-color: rgb(197, 38, 38);
         color: white;
         height: 150px;
         text-align: center;
         word-spacing: 20px;
         /* 词之间的间距 */
         padding-top: 10px;
         letter-spacing: 5px;
         line-height: 40px;
     }
#include <iostream>
using namespace std;

//1.结构体定义
//struct 结构体名 {结构体成员变量列表}
struct Book{
    string name;
    double price;
    int value;
}cpp;

int main(){
    //创建一个结构体数组
    //Book 数组名[元素个数] = { {}，{}，{}...};
    Book books[3] = {
        {"C语言程序设计", 99, 52},
        {"Python零基础",100, 62},
        {"C++零基础",250, 231}
    };

    books[2].name="C语言从入门到入土";
    for(int i=0; i<=3; i++){
        cout<<books[i].name<<" "<<books[i].price<<" "<<books[i].value<<endl;
    }

    return 0;

}
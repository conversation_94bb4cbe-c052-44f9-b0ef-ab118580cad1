/*
问题描述
统计给定文本文件中汉字的个数。
 
输入
输入文件首先包含一个整数n，表示测试实例的个数，然后是n段文本。
 
输出
对于每一段文本，输出其中的汉字的个数，每个测试实例的输出占一行。
[提示：]从汉字机内码的特点考虑~

 
样本输入
2
WaHaHa! WaHaHa! 今年过节不说话要说只说普通话WaHaHa! WaHaHa!
马上就要期末考试了Are you ready?
 
示例输出
14
9
*/

#include <iostream>
#include <cstring>
using namespace std;

int main(){
    char s[500];
    int n;
    cin >> n;
    getchar();
    while(n--){
        gets(s);
        int cnt = 0;
        int len = strlen(s);
        for(int i=0; i<len; ++i){
            if(s[i] < 0){
                ++cnt;
            }
        }
        cout << cnt / 2 <<endl;
    }
    return 0;
}
#if嵌套
"""
num = int(input ("输入一个数字："))
if num % 2 == 0:
    if num % 3 == 0:
        print("你输入的数字可以整除2和3")
    else:
        print("你输入的数字可以整除2,但不能整除3")
else:
    if num % 3 == 0:
        print("你输入的数字可以整除3,但不能整除2")
    else:
        print("你输入的数字不能整除2和3")


#match...case语句
def http_error(status):
    match status:
        case 400:
            return "Bad request"
        case 404:
            return "Not found"
        case 418:
            return "I am a teapot"
        case _:
            return "Something is wrong with the internet"
mystatus = 404
print(http_error(400))
"""

#while循环
#计算1-100的总和
n = 100

sum = 0
counter = 1
while counter <= n:
    sum =sum+counter
    counter +=1

print("1到%d之和为:%d" % (n,sum))

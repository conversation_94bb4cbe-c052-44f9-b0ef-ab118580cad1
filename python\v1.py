str = '123456789'

print(str)                 #输出字符串
print(str[0:-1])           #输出第一个到倒数第二个的所有字符串
print(str[0])              #输出第一个字符串
print(str[2:5])            #输出第三个到第五个字符串
print(str[2:])             #输出第三个到最后一个字符串
print(str[1:5:2])          #输出第二个到第五个字符串 步长为2
print(str[1:6:1])

print(str *2 )              #输出字符串两遍
print(str +'你好')          #输出字符串 加 “你好”

print('----------------------')

print('hello\nrunoob')      #使用反斜杠(\) +n转义特殊字符
print(r'hello\nrunoob')     #在字符串前面加一个 r,表示原始字符串，不会发生转义

#input("\n\n按下enter键后退出。")

import sys; x ='runoob'; sys.stdout.write(x + '\n')

#print输出
x="a"
y="b"
#换行输出
print(x)
print(y)

print('------------')
#不换行输出
print(x,end = "")
print(y,end = "")
print()
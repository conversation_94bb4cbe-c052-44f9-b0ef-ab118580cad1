#re.match函数
'''
   匹配成re.match方法返回一个匹配对象，否则返回None
   函数语法：re.match(pattern,string,flag=0)
   我们可以用group(num)或groups()匹配对象函数来获取匹配表达式
'''
#实例
import re
print(re.match('www','www.runoob.com').span())#在起始位置匹配
print(re.match('com','www.runoob.com'))       #不在起始位置匹配

#实例
import re

line ="cats are smarter than dogs"
#  .*     表示任意匹配除换行符（\n、\r）之外的任何单个或多个字符
#  (.*?)  表示"非贪婪"模式，只保存第一个匹配到的子串
matchObj = re.match(r'(.*) are (.*?) .*',line,re.M|re.I)

if matchObj:
    print("matchObj.group():",matchObj.group())
    print("matchObj.group(1):",matchObj.group(1))
    print("matchObj.group(2):",matchObj.group(2))
else:
    print("No match!!")

#re.search 方法
'''
   re.search 扫描整个字符串并返回第一个成功的匹配
   函数语法：
   re.search(pattern,string,flags=0)

   pattern  匹配的正则表达式
   string   要匹配的字符串
   flags    标志位，用于控制正则表达式的匹配方式，如：是否区分大小写，多行匹配等
'''
#实例
import re
print(re.search('www','www.runoob.com').span())  #在起始位置匹配
print(re.search('com','www.runoob.com').span())  #不在起始位置匹配
#结果 (0,3)  (11,14)
#实例
import re
a = "cats are smarter than dogs"
searchObj = re.search(r'(.*) are (.*?) .*',a,re.M|re.I)

if searchObj:
    print("searchObj.group():",searchObj.group())
    print("searchObj.group(1):",searchObj.group(1))
    print("searchObj.group(2):",searchObj.group(2))
else:
    print("Nothing found!!")

'''re.match & re.search 的区别
   re.match 只匹配字符串的开始，如果字符串开始不符合正则表达式，则匹配失败，函数返回None，
   而re.search 匹配整个字符串，直到找到下一个匹配
'''
#实例
import re
b = "cats are smarter than dogs"

matchObj = re.match(r'dogs',b,re.M|re.I)
if matchObj:
    print('match --> matchObj.group():',matchObj.group())
else:
    print("No match!!")

matchObj = re.search(r'dogs',b,re.M|re.I)
if matchObj:
    print('search --> matchObj.group():',matchObj.group())
else:
    print("No match!!")

#检索和替换
'''
    re.sub 用于替换字符串中的匹配项
    语法:  re.sub(pattern,repl,string,count=0,flags=0)
参数：
    repl：   替换的字符串，也可以为一个函数
    count：  模式匹配后替换的最大次数，默认0表示替换所有的匹配
    string： 要被查找替换的原始字符串
    flags：  编译使用的匹配模式，数字形式
前三个(pattern,repl,string)为必选,后两个为可选参数
'''
#实例
import re
phone = "2004-959-559 #这是一个电话号码"

#删除注释
num = re.sub(r'#.*$',"",phone)
print("电话号码：",num)

#移除非数字的内容
num = re.sub(r'\D',"",phone)
print("电话号码：",num)

#repl参数是一个函数
#例子
import re
# 将匹配的数字乘以2
def double(m):
    a = int(m.group('a'))
    return str(a*2)
s ='a23g4hfd567'
print(re.sub('(?P<a>\d+)',double,s))

#compile 函数
'''compile 函数用于编译正则表达式，生成一个正则表达式(pattern)对象，
   供match()和search()这两个函数使用'''
#compile 例子
import re
pattern = re.compile(r'\d+')                   # 用于匹配至少一个数字
m = pattern.match('one12twothree34four')       # 查找头部，没有匹配
print(m)
m = pattern.match('one12twothree34four',2,10)  # 从'e'的位置开始匹配，没有匹配
print(m)
m = pattern.match('one12twothree34four',3,10)  # 从'1'的位置开始匹配，正好匹配
print(m)    # 返回一个 Match 对象 <_sre.SRE_Match object at 0x10a42aac0>
m.group(0)  
m.start(0)  
m.end(0)
m.span(0)
# 以上可省略 0
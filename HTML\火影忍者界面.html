<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>火影忍者手游注册</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: url('img/11.png') no-repeat center center;
            background-size: cover;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        
        .login-container h2 {
            text-align: center;
            color: #333;
        }
        
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            display: inline-block;
            border: 1px solid #ccc;
            box-sizing: border-box;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            padding: 14px 20px;
            margin: 8px 0;
            border: none;
            cursor: pointer;
            width: 100%;
        }
        
        button:hover {
            opacity: 0.8;
        }
    </style>
</head>

<body>

    <div class="login-container">
        <h2>欢迎注册火影忍者手游</h2>
        <form id="registerForm">
            <label for="username"><b>用户名</b></label>
            <input type="text" placeholder="输入用户名" name="username" required>

            <label for="password"><b>密码</b></label>
            <input type="password" placeholder="输入密码" name="password" required>

            <label for="confirmPassword"><b>确认密码</b></label>
            <input type="password" placeholder="确认密码" name="confirmPassword" required>

            <button type="submit">注册</button>
        </form>
    </div>

    <script>
        document.getElementById('registerForm').addEventListener('submit', function(event) {
            event.preventDefault(); // 阻止表单默认提交行为
            var username = this.username.value;
            var password = this.password.value;
            var confirmPassword = this.confirmPassword.value;

            // 验证用户名和密码
            if (!username) {
                alert('用户名不能为空！');
                return;
            }

            // 验证密码复杂度
            if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$/.test(password)) {
                alert('密码必须至少8位，并且包含大小写字母及数字组合！');
                return;
            }

            // 验证密码是否一致
            if (password !== confirmPassword) {
                alert('两次输入的密码不一致！');
                return;
            }

            // 这里可以添加更多的验证逻辑，例如检查用户名是否已存在等

            // 如果验证成功，可以在这里添加发送数据到服务器的代码
            // 假设注册成功，跳转到登录页面
            window.location.href = 'login.html'; // 假设登录页面是login.html
        });
    </script>

</body>

</html>
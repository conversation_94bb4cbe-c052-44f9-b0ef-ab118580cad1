#include <iostream>
#include <stdexcept> // 引入异常处理库

using namespace std; // 使用标准命名空间

//先实现一个queue类
template<typename T>
class Queue{
    private:
    T *data; //动态数组
    int front; //队首
    int rear; //队尾
    int capacity;
    void resize(); // 扩容函数的声明

    // 构造函数，初始化队列
    public:
    Queue() : data(new T[capacity]), front(0), rear(0), capacity(10){}
    ~Queue(); // 析构函数，释放动态分配的内存
    void enqueue(T element); //入队
    T dequeue();  //出队
    T getFront() const;  //获取队首元素
    int getSize() const;  //获取队列的大小
};

//实现扩容函数
template<typename T>
void Queue<T>::resize(){
    //申请新的内存空间
    T *newData = new T[capacity *2];
    // 将旧数据复制到新数组中
    for(int i=0; i<rear; ++i){
        newData[i] = data[i];
    }
    delete[] data; // 释放旧数组
    data = newData; // 更新指针和容量
    capacity *=2;

}

template<typename T>
Queue<T>::~Queue(){
    delete[] data; // 释放动态分配的内存
}

//入队
template<typename T>
void Queue<T>::enqueue(T element){
    //若队列已满，则调用扩容函数
    if(rear == capacity){
        resize();
    }
    //将元素添加到队尾
    data[rear++] = element;
}

//出队
template<typename T>
T Queue<T>::dequeue(){
    if(front == rear){
        throw std::underflow_error("Queue is empty");
    }
    // 返回队首元素并将其从队列中移除
    return data[front++];
}

template<typename T>
T Queue<T>::getFront() const{
    if(front == rear){
        throw std::underflow_error("Queue is empty");
    }
    // 返回队首元素
    return data[front];
}

template<typename T>
int Queue<T>::getSize() const{
    return rear-front;
}


int main(){
    Queue<int> q;
    q.enqueue(30);
    q.enqueue(4);
    cout<<q.getFront()<<endl;
    q.enqueue(5);
    cout<<q.getFront()<<endl;
    q.dequeue();
    cout<<q.getFront()<<endl;
    cout<<q.getSize()<<endl;

    return 0;
}
#include <iostream>
#include <vector>

void rearrangeArray(std::vector<int>& A) {
    int n = A.size();
    int left = 0; // 左指针
    int right = n - 1; // 右指针

    while (left < right) {
        // 移动左指针直到找到一个偶数
        while (left < right && A[left] % 2 != 0) {
            left++;
        }
        // 移动右指针直到找到一个奇数
        while (right > left && A[right] % 2 == 0) {
            right--;
        }
        // 交换两个指针指向的元素
        if (left < right) {
            std::swap(A[left], A[right]);
        }
    }
}

int main() {
    std::vector<int> A = {1, 2, 3, 4, 5, 6, 7, 8, 9};
    rearrangeArray(A);
    
    // 打印结果
    for (int num : A) {
        std::cout << num << " ";
    }
    std::cout << std::endl;
    
    return 0;
}
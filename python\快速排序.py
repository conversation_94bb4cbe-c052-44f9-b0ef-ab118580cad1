def partition(arr,low,high,compar_count,move_count):
    i = (low-1)
    p = arr[high]

    for j in range(low,high):
        compar_count[0] +=1 #增加比较次数
        # 当前元素小于或等于 pivot
        if arr[j] <= p:
            i = i+1
            arr[i],arr[j]=arr[j],arr[i]
            move_count[0]+=1 #增加移动次数
    arr[i+1],arr[high]=arr[high],arr[i+1]
    move_count[0]+=1 #增加移动次数
    return(i+1)



# arr[] --> 排序数组
# low  --> 起始索引
# high  --> 结束索引

#快速排序函数
def quickSort(arr, low, high, compare_count, move_count):
    if low < high:
        pi = partition(arr, low, high, compare_count, move_count)
        quickSort(arr, low, pi - 1, compare_count, move_count)
        quickSort(arr, pi + 1, high, compare_count, move_count)

compare_count =[0]
move_count =[0]
arr=[10,7,8,9,1,5]
n=len(arr)
quickSort(arr,0,n-1,compare_count,move_count)

print("排序后的数组：")
for i in range(n):
    print("%d"%arr[i]) 

print("比较次数：", compare_count[0])
print("移动次数：", move_count[0])  
#include <iostream>
#include <string>
using namespace std;

//接口，方法，函数 是同一个概念

class Hero {
public:
   void SetName(string name){
      m_Name = name;
   }
   string GetName(){
      return m_Name;
   }

private:
   string  m_Name;        //可读可写
   int     m_SkillCount;  
   int     m_Speed;
};


int main(){
   Hero h;
   /*
     h.m_Name = "123";
     h.m_SkillCount = 4;
     h.m_Speed = 10;
   */

  return 0;

}
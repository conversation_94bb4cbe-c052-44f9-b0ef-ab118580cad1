#include <iostream>
using namespace std;

struct DataS{
    int i;
    double d;
    char s[10];
};

//联合体，所有成员都从相同的内存地址开始
//所有联合体的成员 他们的起始地址都是一模一样的
union DataU{
    int i;   //4字节
    double d;  //8字节
    char s[10]; //10字节
};

/*
1.定义和使用分开
union DataU{
    int i;   //4字节
    double d;  //8字节
    char s[10]; //10字节
};

2.定义和使用结合
union DataU{
    int i;   //4字节
    double d;  //8字节
    char s[10]; //10字节
}a, b, c;

3.匿名：不想让别人使用
union {
    int i;   //4字节
    double d;  //8字节
    char s[10]; //10字节
}a, b, c;
*/

int main(){
    DataS ds;
    cout<<&ds.i<<","<<&ds.d<<","<</*拿地址*/(void *)ds.s<<endl;
  //0x61fef0,0x61fef8,0x61ff00
    DataU du;
    cout<<&du.i<<","<<&du.d<<","<</*拿地址*/(void *)du.s<<endl;
  //0x61fee0,0x61fee0,0x61fee0
}
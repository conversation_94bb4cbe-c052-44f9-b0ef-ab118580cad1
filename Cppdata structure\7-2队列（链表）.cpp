#include <iostream>  // 引入输入输出流库
#include <stdexcept> // 引入异常处理库

using namespace std; // 使用标准命名空间

// 定义一个模板类 Queue，用于实现队列
template<typename T>
class Queue {
private:
    struct Node {  // 定义节点结构体
        T data;    // 节点存储的数据
        Node *next; // 指向下一个节点的指针
        Node(T d) : data(d), next(NULL) {}  // 构造函数，初始化节点的数据和指针
    };

    Node *front; // 队首指针
    Node *rear;  // 队尾指针
    int size;    // 队列的大小

public:
    Queue() : front(NULL), rear(NULL), size(0) {}  // 构造函数，初始化队列为空
    ~Queue();  // 析构函数的声明

    void enqueue(T element);  // 入队操作的声明
    T dequeue();  // 出队操作的声明
    T getFront() const;  // 获取队首元素的声明
    int getSize() const;  // 获取队列大小的声明
};

// 析构函数的实现
template<typename T>
Queue<T>::~Queue() {
    while (front) {  // 当队列不为空时
        Node *temp = front;  // 保存当前队首节点
        front = front->next; // 将队首指针移动到下一个节点
        delete temp;  // 删除当前节点
    }
}

// 入队操作的实现
template<typename T>
void Queue<T>::enqueue(T element) {
    if (rear == NULL) {  // 如果队列为空
        rear = new Node(element);  // 创建新节点并设置为队尾
        front = rear;  // 同时设置为队首
    } else {
        rear->next = new Node(element);  // 在队尾添加新节点
        rear = rear->next;  // 更新队尾指针
    }
    ++size;  // 增加队列大小
}

// 出队操作的实现
template<typename T>
T Queue<T>::dequeue() {
    if (front == NULL) {  // 如果队列为空
        throw std::underflow_error("Queue is empty");  // 抛出异常
    }
    T element = front->data;  // 获取队首元素的数据
    Node *temp = front;  // 保存当前队首节点
    front = front->next;  // 将队首指针移动到下一个节点
    delete temp;  // 删除当前节点
    --size;  // 减少队列大小
    return element;  // 返回队首元素的数据
}

// 获取队首元素的实现
template<typename T>
T Queue<T>::getFront() const {
    if (front == NULL) {  // 如果队列为空
        throw std::underflow_error("Queue is empty");  // 抛出异常
    }
    return front->data;  // 返回队首元素的数据
}

// 获取队列大小的实现
template<typename T>
int Queue<T>::getSize() const {
    return size;  // 返回队列的大小
}

// 主函数
int main() {
    Queue<int> q;  // 创建一个整数类型的队列
    q.enqueue(30);  // 将 30 入队
    q.enqueue(4);   // 将 4 入队
    cout << q.getFront() << endl;  // 输出队首元素，应为 30
    q.enqueue(5);  // 将 5 入队
    cout << q.getFront() << endl;  // 输出队首元素，应仍为 30
    q.dequeue();  // 将队首元素出队
    cout << q.getFront() << endl;  // 输出新的队首元素，应为 4
    cout << q.getSize() << endl;   // 输出队列的大小，应为 2
}
#include <iostream> // 包含标准输入输出流库
#include <stdexcept> // 包含标准异常库

using namespace std; // 使用标准命名空间

template<typename T> // 定义一个模板类Stack
class Stack{
private:
    T *data; // data是顺序表的一个首地址
    int size; // 栈的当前大小
    int capacity; // 栈的容量
    void resize(); // 扩容的私有方法声明

public:
    // 构造函数，初始化容量为10
    Stack():data(new T[capacity]), size(0), capacity(10){}
    ~Stack(); // 析构函数声明
    // 入栈操作
    void push(T element);
    T pop(); // 出栈操作，弹出一个元素
    T top() const; // 获取栈顶元素，不需要修改成员变量
    int getSize() const; // 获取栈的实际大小
};

// 扩容操作的实现
template<typename T>
void Stack<T>::resize(){
    int newCapacity = capacity * 2; // 新容量为当前容量的两倍
    T *newData = new T[newCapacity]; // 创建新的数组
    for(int i=0; i<size; ++i){ // 将旧数组的元素复制到新数组
        newData[i] = data[i];
    }
    delete[] data; // 删除旧数组
    data = newData; // 更新data指针指向新数组
    capacity = newCapacity; // 更新容量
}

// 顺序表的析构函数实现
template<typename T>
Stack<T>::~Stack(){
    delete[] data; // 删除数组，释放内存
}

// 入栈操作的实现
template<typename T>
void Stack<T>::push(T element){
    if(size == capacity){ // 如果栈满了，则扩容
        resize();
    }
    data[size++] = element; // 将元素添加到栈顶，并更新栈的大小
}

// 出栈操作的实现
template<typename T>
T Stack<T>::pop(){
    if(size==0){ // 如果栈为空，则抛出异常
        throw std::underflow_error("Stack is empty");
    }
    return data[--size]; // 返回栈顶元素，并更新栈的大小
}

// 获取栈顶元素的实现
template<typename T>
T Stack<T>::top() const{
    if(size==0){ // 如果栈为空，则抛出异常
        throw std::underflow_error("Stack is empty");
    }
    return data[size-1]; // 返回栈顶元素
}

// 获取栈的实际大小的实现
template<typename T>
int Stack<T>::getSize() const{
    return size; // 返回栈的实际大小
}

int main(){
    Stack<int> st; // 创建一个int类型的栈
    st.push(4); // 将4入栈
    st.push(7); // 将7入栈
    st.push(13); // 将13入栈
    cout<<st.top()<<endl; // 输出栈顶元素
    st.push(17); // 将17入栈
    cout<<st.top()<<endl; // 输出栈顶元素
    st.pop(); // 弹出栈顶元素
    st.pop(); // 弹出栈顶元素
    cout<<st.top()<<endl; // 输出栈顶元素
    cout<<st.getSize()<<endl; // 输出栈的实际大小

    return 0; // 程序结束
}
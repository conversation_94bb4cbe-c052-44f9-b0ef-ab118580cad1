<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>千年之恋</title>
    <style>
        #head {
            width: 980px;
            height: 70px;
            margin: 0 auto;
            padding-left: 20px;
        }
        
        #nav_bg {
            width: 100%;
            height: 48px;
            background: #fe668f;
        }
        
        .nav {
            width: 980px;
            margin: 0 auto;
        }
        
        .nav li {
            float: left;
        }
        
        a {
            display: inline-block;
            height: 48px;
            padding: 0 50px;
            line-height: 48px;
        }
        
        .bg {
            background: #fe9ab5;
        }
        
        a:hover {
            background: #fe9ab5;
        }
        
        #banner {
            width: 980px;
            margin: 0 auto;
        }
        
        #content {
            width: 830px;
            height: 934px;
            background: url()no-repeat;
            margin: 0 auto;
            padding-left: 150px;
        }
        
        .step {
            width: 454px;
            height: 80px;
            font-size: 20px;
            font-weight: 100;
            color: aqua;
            line-height: 45px;
            background: url() center right no-repeat;
        }
        
        h3 {
            width: 444px;
            height: 45px;
            font-size: 20px;
            font-weight: 100;
            color: aqua;
            line-height: 45px;
            border-bottom: 1px solid #dd8787;
        }
        
        td {
            height: 50px;
            color: #dd8787;
        }
        
        .left {
            width: 120px;
            text-align: right;
        }
        
        .right {
            width: 320px;
            height: 28px;
            border: 1px solid #dd8787;
        }
        
        input {
            vertical-align: middle;
        }
        
        select {
            width: 171px;
            border: 1px solid #dd8787;
            color: #dd8787;
        }
        
        textarea {
            width: 380px;
            border: 1px solid #dd8787;
            resize: none;
            font-size: 12px;
            color: #aaa;
            padding: 20px;
        }
        
        .btn {
            width: 408px;
            height: 76px;
            background: url() right center no-repeat;
        }
    </style>
</head>

<body>
    <!--头部-->
    <div id="head">
        <img src="img/木叶.jpg" />
    </div>
    <!--导航-->
    <div id="nav_bg"></div>
    <ul class="nav">
        <li><a href="#">首页</a></li>
        <li><a href="#">会员</a></li>
        <li><a href="#">活动</a></li>
        <li><a href="#">直播</a></li>
        <li><a href="#">视频</a></li>
        <li><a href="#" class="bg">注册</a></li>
    </ul>

    <div id="banner">
        <img src="" />
    </div>

    <div id="content">
        <h2 class="step">注册步骤：</h2>
        <from action="#" method="post" class="one">
            <h3>您的账号信息：</h3>
            <table class="content">
                <tr>
                    <td class="left">注册方式：</td>
                    <td>
                        <label for="one"><input type="radio" name="sex" id="one" />E-mail注册</label>&nbsp;&nbsp;&nbsp;&nbsp;
                        <label for="two"><input type="radio" name="sex" id="two" />手机号码注册</label>
                    </td>
                </tr>

                <tr>
                    <td class="left">注册邮箱：</td>
                    <td><input type="text" class="right" /></td>
                </tr>

                <tr>
                    <td class="left">注册手机：</td>
                    <td><input type="text" class="right" /></td>
                </tr>

                <tr>
                    <td class="left">登录密码：</td>
                    <td><input type="password" maxlength="8" class="right" /></td>
                </tr>

                <tr>
                    <td class="left">昵称：</td>
                    <td><input type="text" class="right" /></td>
                </tr>
            </table>
            <h3>您的个人信息：</h3>
            <table class="content">
                <tr>
                    <td class="left">性别：</td>
                    <td>
                        <label for="boy"><input type="radio" name="sex" id="boy" />男</label>&nbsp;&nbsp;&nbsp;&nbsp;
                        <label for="girl"><input type="radio" name="sex" id="girl" />女</label>
                    </td>
                </tr>
                <tr>
                    <td class="left">学历：</td>
                    <td>
                        <select>
                            <option>-请选择-</option>
                            <option>中职/高中</option>
                            <option>专科/本科</option>
                            <option>硕士研究生</option>
                            <option>博士研究生</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="left">所在城市：</td>
                    <td>
                        <select>
                            <option>-请选择-</option>
                            <option selceted="aelceted">北京</option>
                            <option>上海</option>
                            <option>广州</option>
                            <option>深圳</option>
                        </select>
                </tr>
                <tr>
                    <td class="left">兴趣爱好：</td>
                    <td>
                        <input type="checkbox" />足球&nbsp;&nbsp;&nbsp;
                        <input type="checkbox" />篮球&nbsp;&nbsp;&nbsp;
                        <input type="checkbox" />游泳&nbsp;&nbsp;&nbsp;
                        <input type="checkbox" />唱歌&nbsp;&nbsp;&nbsp;
                        <input type="checkbox" />跑步&nbsp;&nbsp;&nbsp;
                        <input type="checkbox" />瑜伽
                    </td>
                </tr>
                <tr>
                    <td class="left">自我介绍：</td>
                    <td><textarea clos="60" rows="8">00000000000000000000000000000000</textarea>

                    </td>
                </tr>
                <tr>
                    <td colspan="2"><input type="button" class="btn" /></td>
                </tr>
            </table>
        </from>

    </div>
    <div id="footer"></div>

</body>

</html>
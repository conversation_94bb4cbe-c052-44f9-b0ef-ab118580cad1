#include <iostream>

// 快速幂算法求 a^n mod m
long long mod_pow(long long a, long long n, long long m) {
    long long result = 1;
    a = a % m; // 首先对a取模，以确保a在mod m的范围内
    while (n > 0) {
        // 如果n是奇数，将当前的a乘到result上，并更新result
        if (n % 2 == 1) {
            result = (result * a) % m;
        }
        // 将a平方，并更新n
        a = (a * a) % m;
        n = n >> 1; // 将n右移一位，相当于n除以2
    }
    return result;
}

int main() {
    long long a, n, m;
    std::cout << "Enter a, n, m: ";
    std::cin >> a >> n >> m;
    std::cout << "Result: " << mod_pow(a, n, m) << std::endl;
    return 0;
}
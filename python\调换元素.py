#将列表中的头尾两个元素对调
#例如：对调前[1,2,3]
#     对调后[3,2,1]
def list(newlist):
    size = len(newlist)

    temp = newlist[0]
    newlist[0] = newlist[size - 1]
    newlist[size - 1] = temp

    return newlist
newlist=[1,2,3]
print(list(newlist))


#将列表中指定位置的两个元素对调
'''对调前 : List = [23, 65, 19, 90], pos1 = 1, pos2 = 3
   对调后 : [19, 65, 23, 90]
'''
def swapPositions(list,pos1,pos2):
    list[pos1],list[pos2] = list[pos2],list[pos1]
    return list
list =[23, 65, 19, 90]
pos1,pos2 = 1,3
print(swapPositions(list,pos1-1,pos2-1))


#include <iostream>
#include <vector>
using namespace std;

// 函数来找到数组中的最大元素
int findMax(const vector<int>& arr, int low, int high) {
    if (low == high) { // 如果数组只有一个元素
        return arr[low];
    }

    int mid = low + (high - low) / 2; // 找到中间点

    // 递归地在左半部分和右半部分找到最大值
    int leftMax = findMax(arr, low, mid);
    int rightMax = findMax(arr, mid + 1, high);

    // 返回左右两个最大值中较大的那个
    return max(leftMax, rightMax);
}

int main() {
    vector<int> arr = {12, 11, 13, 5, 6, 7};
    int n = arr.size();
    if (n == 0) {
        cout << "数组为空，无法找到最大元素。";
        return 1;
    }
    cout << "数组中的最大元素是: " << findMax(arr, 0, n - 1) << endl;
    return 0;
}
#隐式类型转换
num_int =123
num_flo =1.23

num_new = num_int + num_flo

print("num_int 数据类型为：",type(num_int))
print("num_flo 数据类型为：",type(num_flo))

print("num_new 值为：",num_new)
print("num_new 数据类型为：",type(num_new))

#运算符
a = 21
b = 10
c = 0

c = a + b
print("1-c的值为：",c)
c = a - b
print("2-c的值为：",c)
c = a * b
print("3-c的值为：",c)
c = a / b
print("4-c的值为：",c)
c = a % b
print("5-c的值为：",c)

#修改变量
a = 2
b = 3
c = a**b
print("6-c的值为：",c)

a = 10
b = 5
c = a//b
print("7-c的值为：",c)

a = 60
b = 13
c = 0

c = a & b
print("1 - c的值为：",c)
c = a | b
print("2 - c的值为：",c)
c = a ^ b
print("3 - c的值为：",c)
c = ~a
print("4 - c的值为：",c)
c = a << 2
print("5 - c的值为：",c)
c = a >> 2
print("6 - c的值为：",c)

a = 9
b = 2
c = 0

c = a // b
print("c的值为：",c)

x = True
y = False
z = False

if x or y and z:
    print("yes")
else:
    print("no")

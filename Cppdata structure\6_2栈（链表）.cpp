#include <iostream> // 包含标准输入输出流库
#include <stdexcept> // 包含标准异常库

using namespace std; // 使用标准命名空间

// 定义一个模板类Stack，用于创建栈
template<typename T>
class Stack{
private:
    struct Node{ // 定义一个私有的Node结构体，用于栈的节点
        T data; // 节点存储的数据
        Node *next; // 指向下一个节点的指针
        Node(T d):data(d), next(NULL){} // 构造函数，初始化数据和下一个节点
    };
    Node *head; // 指向栈顶元素的指针
    int size; // 栈中元素的数量
public:
    // 构造函数，初始化栈顶指针和栈的大小
    Stack():head(NULL), size(0){}
    ~Stack(); // 析构函数声明
    void push(T element); // 入栈操作的声明
    T pop(); // 出栈操作的声明
    T top() const; // 获取栈顶元素的声明
    int getSize() const; // 获取栈中元素数量的声明
};

// 栈的析构函数实现，用于释放内存
template<typename T>
Stack<T>::~Stack(){
    while(head){ // 循环直到栈为空
        Node *temp = head; // 临时保存当前栈顶节点
        head = head->next; // 移动栈顶指针到下一个节点
        delete temp; // 删除当前节点，释放内存
    }
}

// 入栈操作的实现
template<typename T>
void Stack<T>::push(T element){
    Node *newNode = new Node(element); // 创建一个新的节点
    newNode->next = head; // 新节点的下一个节点是当前的栈顶节点
    head = newNode; // 更新栈顶指针为新节点
    ++size; // 栈的大小增加
}

// 出栈操作的实现
template<typename T>
T Stack<T>::pop(){
    if(head == NULL){ // 如果栈为空，则抛出异常
        throw std::underflow_error("Stack is empty");
    }
    T result = head->data; // 获取栈顶元素的数据
    Node *temp = head; // 临时保存当前栈顶节点
    head = head->next; // 移动栈顶指针到下一个节点
    delete temp; // 删除当前节点，释放内存
    --size; // 栈的大小减少
    return result; // 返回被弹出的元素
}

// 获取栈顶元素的实现
template<typename T>
T Stack<T>::top() const{
    if(head == NULL){ // 如果栈为空，则抛出异常
        throw std::underflow_error("Stack is empty");
    }
    return head->data; // 返回栈顶元素的数据
}

// 获取栈中元素数量的实现
template<typename T>
int Stack<T>::getSize() const{
    return size; // 返回栈中元素的数量
}

int main(){
    Stack<int> st; // 创建一个int类型的栈
    st.push(4); // 将4入栈
    st.push(7); // 将7入栈
    st.push(13); // 将13入栈
    cout<<st.top()<<endl; // 输出栈顶元素
    st.push(17); // 将17入栈
    cout<<st.top()<<endl; // 输出栈顶元素
    st.pop(); // 弹出栈顶元素
    st.pop(); // 弹出栈顶元素
    cout<<st.top()<<endl; // 输出栈顶元素
    cout<<st.getSize()<<endl; // 输出栈中元素的数量

    return 0; // 程序结束
}
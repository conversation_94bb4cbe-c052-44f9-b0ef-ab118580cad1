class solution:
    def merge(self, nums1:list[int], m:int, nums2:list[int], n:int) -> None:
        p1, p2, p = m-1, n-1, m+n-1
        while p2 >=0:
            #nums2 还有要合并的元素
            #如果 p2<0,那么走else分支，把nums2合并到nums1中
            if p1 >= 0 and nums1[p1] >nums2[p2]:
                nums1[p] = nums1[p1] #填入nums1[p1]
                p1 -=1
            else:
                nums1[p] = nums2[p2] #填入nums2[p1]
                p2 -=1
            p -=1 #下一个要填写的位置

/* styles.css */

body {
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin: 0;
}

.sidebar {
    width: 200px;
    background: #2c3e50;
    color: white;
    padding: 20px;
}

.sidebar ul {
    list-style-type: none;
    padding: 0;
}

.sidebar li {
    margin-bottom: 10px;
}

.header {
    background: #34495e;
    color: white;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content {
    flex-grow: 1;
    padding: 20px;
}

.order-details {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
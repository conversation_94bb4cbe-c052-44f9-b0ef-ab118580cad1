#include <iostream>
#include <unordered_set>
#include <vector>
using namespace std;

void removeDuplicates(vector<int>& nums) {
    if (nums.empty()) return;

    // 使用一个unordered_set来记录已经出现过的元素
    unordered_set<int> seen;
    int insertPos = 0; // 用于记录下一个非重复元素应该插入的位置

    for (int i = 0; i < nums.size(); ++i) {
        if (seen.find(nums[i]) == seen.end()) {
            // 如果元素不在seen中，说明是第一次出现，将其插入到insertPos的位置
            nums[insertPos++] = nums[i];
            seen.insert(nums[i]);
        }
    }

    // 将数组中重复元素后面的部分全部置为0，或者可以删除它们，取决于具体需求
    while (insertPos < nums.size()) {
        nums[insertPos++] = 0; // 或者可以进行其他操作，比如 nums.erase(nums.begin() + insertPos);
    }
}

int main() {
    vector<int> nums = {0, 0, 1, 1, 1, 2, 2, 3, 4, 4, 4, 4, 5, 5};
    removeDuplicates(nums);

    cout << "删除重复项后的数组: ";
    for (int num : nums) {
        if (num != 0) { // 打印非零元素
            cout << num << " ";
        }
    }
    cout << endl;

    return 0;
}
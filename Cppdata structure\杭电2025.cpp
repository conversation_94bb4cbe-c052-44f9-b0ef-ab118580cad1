/*
问题描述
对于输入的每个字符串，查找其中的最大字母，在该字母后面插入字符串“（max）”。
 
输入
输入数据包括多个测试实例，每个实例由一行长度不超过100的字符串组成，字符串仅由大小写字母构成。
 
输出
对于每个测试实例输出一行字符串，输出的结果是插入字符串“（max）”后的结果，如果存在多个最大的字母，就在每一个最大字母后面都插入“（max）”。
 
样本输入
abcdefgfedcba
xxxxx
 
示例输出
abcdefg(max)fedcba
x(max)x(max)x(max)x(max)x(max)
*/

#include <iostream>
#include <string>
using namespace std;

int main(){
    string s;
    while(cin >> s){
        string ret = "";
        char maxc = 'a';
        for(int i=0; i<s.size(); ++i){
            if(s[i] > maxc){
                maxc = s[i];
            }
        }
        for(int i=0; i<s.size(); ++i){
            ret = ret+s[i];
            if(s[i] == maxc){
                ret = ret+"(max)";
            }
        }
        cout<<ret<<endl;
    }

    return 0;
}
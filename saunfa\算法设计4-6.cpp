#include <iostream>
#include <queue>
#include <vector>
using namespace std;

// 函数来找到集合中小于n的元素个数
int countElements(int n) {
    int count = 0;
    for (int x = 0; 2 << x < n; x++) {
        for (int y = 0; 2 << x * 3 << y < n; y++) {
            if (2 << x * 3 << y < n) {
                count++;
            }
        }
    }
    return count;
}

// 函数来找到集合中小于n的元素从小到大排序的第m项元素
long long findMthElement(int n, int m) {
    priority_queue<long long> pq;
    for (int x = 0; 2 << x < n; x++) {
        for (int y = 0; 2 << x * 3 << y < n; y++) {
            pq.push(2 << x * 3 << y);
        }
    }
    for (int i = 1; i < m; i++) {
        pq.pop();
    }
    return pq.top();
}

int main() {
    int n, m;
    cout << "请输入整数 n: ";
    cin >> n;
    cout << "请输入整数 m: ";
    cin >> m;

    int count = countElements(n);
    long long mthElement = findMthElement(n, m);

    cout << "集合中小于 " << n << " 的元素个数是: " << count << endl;
    cout << "这些元素从小到大排序的第 " << m << " 项元素是: " << mthElement << endl;

    return 0;
}
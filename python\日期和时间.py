import time #引入time模块
ticks= time.time()
print("当前时间戳为：",ticks)

#获取当前时间
import time
localtime = time.localtime(time.time())
print("本地时间为：",localtime)

#获取格式化的时间
localtime = time.asctime(time.localtime(time.time()))
print("本地时间为：",localtime)

#格式化日期
#time.strftime(format[, t])
import time
# 格式化成2016-03-20 11:45:39形式
print(time.strftime("%Y-%m-%d %H:%M:%S",time.localtime()))
# 格式化成Sat Mar 28 22:24:24 2016形式
print (time.strftime("%a %b %d %H:%M:%S %Y", time.localtime()))
# 将格式字符串转换为时间戳
a = "Sat Mar 28 22:24:24 2016"
print (time.mktime(time.strptime(a,"%a %b %d %H:%M:%S %Y")))

#获取某月日历
#Calendar 模块有很广泛的方法用来处理年历和月历，例如打印某月的月历

import calendar

cal = calendar.month(2024,4)
print("以下输出2024年4月的日历：")
print(cal)
#include <iostream>
#include <stdexcept>
using namespace std;

#define eleType int

//链表结构的结构体
struct ListNode{
    //数据域
    eleType data;
    //指针域
    ListNode *next;
    //构造函数
    ListNode(eleType x):data(x),next(NULL){}
};

//单向链表的类
class LinkedList{
    private:
    ListNode* head;//头结点
    int size;

    public:
    LinkedList():head(NULL),size(0){}
    ~LinkedList();
    //类里的函数声明 插入
    void insert(int i, eleType value);
    //删除
    void remove(int i);
    ListNode* find(eleType value);
    ListNode* get(int i);//代表的一个索引的位置
    //更新
    void update(int i, eleType value);
    void print();
    eleType sum();
};

//析构每个函数的内存空间
LinkedList::~LinkedList(){
    ListNode *curr = head;
    while(curr != NULL){
        //拿到当前节点存储在tmp中
        ListNode *tmp = curr;
        curr = curr->next;//把当前结点置为他的后继结点
        delete tmp;
    }
}

//单向链表的插入
void LinkedList::insert(int i, eleType value){
    //判断是否合法
    if(i<0 || i>size){
        throw std::out_of_range("Invalid position");
    }
    //先生成一个新的结点,调用ListNode函数
    ListNode* newNode = new ListNode(value);
    if(i==0){
        //插入到头结点
        newNode->next = head;
        head = newNode;
    }else{
        //定义一个游标结点
        ListNode *curr = head;
        //不断从head遍历
        for(int j=0; j<i-1; ++j){
            curr = curr->next;
        }
        newNode->next = curr->next;
        curr->next = newNode;
    }
    ++size;
}

//单向链表的删除
void LinkedList::remove(int i){
    if(i<0 || i>=size){
        throw std::out_of_range("Invalid position");
    }
    if(i==0){
        //删除头结点先将其缓存起来
        ListNode *temp = head;
        head = head->next;
        delete temp;
    }else{
        ListNode *curr = head;
        //不断从head遍历
        for(int j=0; j<i-1; ++j){
            curr = curr->next;
        }
        ListNode *temp = curr->next;
        curr->next = temp->next;
        delete temp;
    }
    --size;
}

//单向链表的元素查找
ListNode *LinkedList::find(eleType value){
    ListNode *curr = head;
    while(curr && curr->data != value){
        curr = curr->next;
    }
    return curr;
}

//单向链表的索引查找
ListNode *LinkedList::get(int i){
    if(i<0 || i>=size){
        throw std::out_of_range("Invalid position");
    }
    ListNode *curr = head;
    for(int j=0; j<i; ++j){
        curr = curr->next;
    }
    return curr;
}

//单向链表的更新
void LinkedList::update(int i, eleType value){
    get(i)->data = value;
}

//调试函数
void LinkedList::print(){
    ListNode *curr = head;
    while(curr){
        cout<<curr->data<<" ";
        curr = curr->next;
    }
    cout << endl;
}

eleType LinkedList::sum(){
    ListNode *curr = head;
    eleType ret = 0;
    while(curr != NULL){
        ret += curr->data;
        curr = curr->next;
    }
    return ret;
}

int main(){
    int n;
    while(cin>>n){
        LinkedList l;
        for(int i=0; i<n; ++i){
            int x;
            cin>>x;
            l.insert(i, x);
        }
        //l.print();
        cout<<l.sum()<<endl;
    }
    return 0;
}
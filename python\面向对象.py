#类对象
class MyClass:
    """一个简单的类实例"""
    i= 12345
    def f(self):
        return 'hell world'
    
#实例化类
x = MyClass()
#访问类的属性和方法
print("MyClass 类的属性i为:",x.i)
print("MyClass 类的方法f输出为:",x.f())

#类的方法
class people:
    #定义基本属性
    name = ''
    age =0
    #定义私有属性,私有属性在类外部无法直接进行访问
    __weight =0
    #定义构造方法
    def __init__(self,n,a,w):
        self.name = n
        self.age = a
        self.__weight = w
    def speak(self):
        print("%s 说：我%d岁。" %(self.name,self.age))
#实例化类
p = people('鲁搏',21,30)
p.speak()
#继承 子类会继承父类的属性和方法
'''
class (派生类)DerivedClassName((父类)BaseClassName):
    <statement-1>
    .
    .
    .
    <statement-N>
'''
#实例
#类定义
class student(people):
    garde =''
    def __init__(self,n,a,w,g):
    #调用父类的构函
        people.__init__(self,n,a,w)
        self.grade = g
    #覆写父类的方法
    def speak(self):
        print("%s 说：我%d岁了，我在读 %d 年级"%(self.name,self.age,self.grade))

s =student('lb',21,75,2)
s.speak()

#多继承
'''class DerivedClassName(Base1, Base2, Base3):
    <statement-1>
    .
    .
    .
    <statement-N>
'''
#另一个类，多继承之前的准备
class speaker():
    topic = ''
    name =''
    def __init__(self,n,t):
        self.name = n
        self.topic = t
    def speak(self):
        print('我叫 %s，我是一个演说家，我演讲的主题是 %s'%(self.name,self.topic))
#多继承
class sample(speaker,student):
    a =''
    def __init__(self,n,a,w,g,t):
        student.__init__(self,n,a,w,g)
        speaker.__init__(self,n,t)
test = sample("lb",21,75,4,'Python')
test.speak() 

#方法重写  若父类方法不满足需求可以在子类重写父类方法
class Parent:
    def myMethod(self):
        print('调用父类方法')
class Child(Parent):
    def myMethod(self):
        print('调用子类方法')
c = Child()
c.myMethod()
super(Child,c).myMethod()
#super()是调用父类（超类）的一个方法

#类的私有方法实例
class site:
    def __init__(self,name,url):
        self.name = name    # public
        self.__url = url    # private

    def who(self):
        print('name:',self.name)
        print('url:',self.__url)

    def __foo(self):  # 私有方法
        print('这是私有方法')

    def foo(self):    # 公共方法
        print('z这是公共方法')
        self.__foo()

x =site('菜鸟教程','www.runoob.com')
x.who()   # 正常输出
x.foo()   # 正常输出
x.__foo() # 报错
'''Traceback (most recent call last):
  File "d:\VScodeprojects\day01\面向对象.py", line 112, in <module>
    x.__foo()
    ^^^^^^^
AttributeError: 'site' object has no attribute '__foo'
外部不能调用私有方法
'''
//士兵队列训练问题
/*
某部队进行新兵队列训练，将新兵从一开始按顺序依次编号，并排成一行横队，训练的规则如下：从头开始一至二报数，凡报到二的出列，剩下的向小序号方向靠拢，再从头开始进行一至三报数，凡报到三的出列，剩下的向小序号方向靠拢，继续从头开始进行一至二报数。。。，以后从头开始轮流进行一至二报数、一至三报数直到剩下的人数不超过三人为止。
*/

//输入
//本题有多个测试数据组，第一行为组数N，接着为N行新兵人数，新兵人数不超过5000。

//输出
//共有N行，分别对应输入的新兵人数，每行输出剩下的新兵最初的编号，编号之间有一个空格。

#include <iostream>  // 引入输入输出流库
#include <stdexcept> // 引入异常处理库

using namespace std; // 使用标准命名空间

// 定义一个模板类 Queue，用于实现队列
template<typename T>
class Queue {
private:
    struct Node {  // 定义节点结构体
        T data;    // 节点存储的数据
        Node *next; // 指向下一个节点的指针
        Node(T d) : data(d), next(NULL) {}  // 构造函数，初始化节点的数据和指针
    };

    Node *front; // 队首指针
    Node *rear;  // 队尾指针
    int size;    // 队列的大小

public:
    Queue() : front(NULL), rear(NULL), size(0) {}  // 构造函数，初始化队列为空
    ~Queue();  // 析构函数的声明

    void enqueue(T element);  // 入队操作的声明
    T dequeue();  // 出队操作的声明
    T getFront() const;  // 获取队首元素的声明
    int getSize() const;  // 获取队列大小的声明
};

// 析构函数的实现
template<typename T>
Queue<T>::~Queue() {
    while (front) {  // 当队列不为空时
        Node *temp = front;  // 保存当前队首节点
        front = front->next; // 将队首指针移动到下一个节点
        delete temp;  // 删除当前节点
    }
}

// 入队操作的实现
template<typename T>
void Queue<T>::enqueue(T element) {
    if (rear == NULL) {  // 如果队列为空
        rear = new Node(element);  // 创建新节点并设置为队尾
        front = rear;  // 同时设置为队首
    } else {
        rear->next = new Node(element);  // 在队尾添加新节点
        rear = rear->next;  // 更新队尾指针
    }
    ++size;  // 增加队列大小
}

// 出队操作的实现
template<typename T>
T Queue<T>::dequeue() {
    if (front == NULL) {  // 如果队列为空
        throw std::underflow_error("Queue is empty");  // 抛出异常
    }
    T element = front->data;  // 获取队首元素的数据
    Node *temp = front;  // 保存当前队首节点
    front = front->next;  // 将队首指针移动到下一个节点
    delete temp;  // 删除当前节点
    --size;  // 减少队列大小
    if(size == 0){
        rear = NULL;
    }
    return element;  // 返回队首元素的数据
}

// 获取队首元素的实现
template<typename T>
T Queue<T>::getFront() const {
    if (front == NULL) {  // 如果队列为空
        throw std::underflow_error("Queue is empty");  // 抛出异常
    }
    return front->data;  // 返回队首元素的数据
}

// 获取队列大小的实现
template<typename T>
int Queue<T>::getSize() const {
    return size;  // 返回队列的大小
}

// 主函数
int main() {
    int t;
    cin>> t;
    while(t--){
        int n;
        cin>>n;
        Queue<int> q1, q2;
        for(int i=1; i<=n; ++i){
            q1.enqueue(i);
        }
        while(q1.getSize()>3){
            int cnt = 0;
            while(q1.getSize()){
                int v = q1.dequeue();
                ++cnt;
                if(cnt % 2 ==1){
                    q2.enqueue(v);
                }
            }
            if(q2.getSize()<=3){
                while(q2.getSize()) q1.enqueue(q2.dequeue());
                break;
            }
            cnt = 0;
            while(q2.getSize()){
                int v = q2.dequeue();
                ++cnt;
                if(cnt % 3 ==1 || cnt % 3 ==2){
                    q1.enqueue(v);
                }
            }
        }
        int flag ;
        while(q1.getSize()){
            if(flag){
                cout<<' ';
            }
            cout<<q1.dequeue();
            flag++;
        }
        cout<<endl;
    }
    return 0;
}

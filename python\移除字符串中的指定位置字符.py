t="runoob"
# 输出原始字符串
print("原始字符串："+t)
# 移除第三个字符 n
n=""
for i in range(0,len(t)):
    if i != 2:
        n = n+t[i]
print("字符串移除后："+n)

#判断字符串是否存在子字符串
def check(string,a):
    if(string.find(a)== -1):
        print("不存在")
    else:
        print("存在")
string ="www.runoob.com"
a ="runoob"
check(string,a)

#判断字符串长度
#使用内置方法len()
s="runoob"
print(len(s))
#使用循环计算
def findlen(s):
    counter = 0
    while s[counter:]:
        counter +=1
        return counter
s="www"
print(findlen(s))
#include <iostream>
using namespace std;

int main() {
	//1.利用指针来访问数组元素
	int a[5] = { 5,4,3,2,1 };
	cout << "数组第一个元素：" << a[0] << endl;

	int* p = a;
	cout << "数组元素首地址：" << a << endl;
	cout << "指针访问的地址：" << p << endl;
	cout << "指针访问：数组的第一个元素" << *p << endl;

	cout << "数组第二个元素的地址：" << &a[1] << endl;
	cout << "指针 + 1 指向的地址：" << p+1 << endl;
	cout << "指针访问数组第二个元素：" << *(p+1) << endl;

	cout << "数组第三个元素的地址：" << &a[2] << endl;
	cout << "指针 + 2 指向的地址：" << p + 2 << endl;
	cout << "指针访问数组第三个元素：" << *(p + 2) << endl;

	p++;
	cout << "指针访问：数组第二个元素：" << *p << endl;

	//a++;

	p = a;
	for (int i = 0; i < 5; ++i) {
		cout << "数组的第" << (i + 1) << "个元素是" << *p << endl;
		p++;

	}
	return 0;
}
#include <iostream>>
using namespace std;

/**
 * ++
 * 前置++
 * 后置++
 */

class Complex{
friend ostream& operator<<(ostream& c, const Complex& a);

    public:
    Complex(): real(0), image(0){}
    Complex(int real, int image){
        this->real = real;
        this->image = image;
    }

    //前置++ 需要引用和本身的对象Complex
    Complex& operator++(){
        this->real += 1;
        return *this;
    }
    //后置++ 返回一个新的对象 需要有个占位符int
    Complex operator++(int){
        Complex c = *this;//临时对象返回不需要指针*和引用&
        this->real += 1;
        return c;
    }

    private:
    int real;
    int image;
};


ostream& operator<<(ostream& c, const Complex& a){
    c<<a.real<<'+'<<a.image<<'i';
    return c;

}

class A{

};
A func1(){
    return A();
}
void func2(const A& a){

}

int main(){
    int x = 1;
    cout<<++(++x)<<endl;
    cout<<x<<endl;

    Complex a(10, 10);
    cout<<a<<endl;
    //++a;
    cout<<++(++a)<<endl;
    cout<<a<<endl;

    cout<<a++<<endl;
    cout<<a<<endl;

    func2(func1());
    
    cout<<((a++)++)++<<endl;
    cout<<a<<endl;

    int b = 5;
    //验证cout<<((b++)++)++<<endl;
    cout<<b<<endl;
    return 0;
}
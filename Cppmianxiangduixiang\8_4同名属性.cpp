#include <iostream>
using namespace std;

class Animal{
    public:
    Animal(){
        m_Date = 123456;
    }
    int m_Date;
};

class Cat:public Animal{
    public:
    Cat(){
        m_Date =2648;
    }
    int m_Date;
};

void Test(){
    Cat c;
    cout<<c.m_Date<<endl;
    cout<<c.Animal::m_Date<<endl;

    cout<<&(c.m_Date)<<endl;
    cout<<&(c.Animal::m_Date)<<endl;
}

int main(){
    Test();
    return 0;
}
/* function cfb() {
    for (i = 1; i <= 9; i++) {
        for (j = 1; j <= i; j++) {
            document.write(i + "x" + j + "=" + i * j + "&nbsp");
        }
        document.write("<br>")
    }
}

function jsp() {
    var a = document.getElementById("a").value;
    var b = document.getElementById("b").value;
    var c = parseFloat(a) + parseFloat(b);
    var jp = document.getElementById("jp");
    if (a.value == "" || b.value == "") {
        jp.innerText = "请输入数字";
    } else {
        jp.innerText = parseFloat(a) * parseFloat(b);
    }


    //alert(jp)
} */


/* 
//验证码
var an = document.getElementById("yzm");

an.onclick = function() {
    var sz = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd',
        'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's',
        't', 'u', 'v', 'w', 'x', 'y', 'z'
    ]

    var yzm = ""

    for (i = 0; i < 4; ++i) {
        var sjs = Math.round(Math.random() * 35)
        yzm += sz[sjs];
    }
    an.value = yzm;
    
    alert()//弹出框
    confirm()//弹出选择框
    prompt()//弹出一个内容框 
} */


var i = 1;
var lbbox = document.getElementById("lbbox");
var next = document.getElementById("lbbox");
var back = document.getElementById("lbbox");

function lb() {

    if (i <= 8) {
        lbbox.innerHTML = "<img src='img/img/" + i + ".jpg'>"
        i++
    } else {
        i = 1;
    }
    $("#lbbox").fadeOut();
    $("#lbbox").fadeIn();
}
var jsq = window.setInterval("lb()", 1000)

window.onload = jsq;


next.onclick = function() {
    window.clearInterval(jsq);
    lb();
}
back.onclick = function() {
    if (i > 0) {
        lbbox.innerHTML = "<img src='img/img/" + i + ".jpg'>"
    } else {
        i = 8;
    }
}
#比较两个数，并返回较大的数
def max(a,b):
    if a>b:
        return a
    else:
        return b
a=4
b=5
print(max(a,b))

#计算面积函数
def 面积(width,height):
    return width*height
def print_welcome(name):
    print("Welcome",name)

print_welcome("runoob")
w=4
h=5
print ("width=",w,"height=",h,"面积=",面积(w,h))

def change(a):
    print(id(a))   # 指向的是同一个对象
    a=1
    print(id(a))   # 一个新对象
 
a=10
print(id(a))
change(a)

#传可变对象实例
#可写函数说明
def changeme(mylist):
    "修改传入的列表"
    mylist.append([1,2,3,4])
    print("函数内取值：",mylist)
    return
#调用changeme函数
mylist = [10,20,30]
changeme(mylist)
print("函数外取值：",mylist)
"传入函数的和在末尾添加新内容的对象用的是同一个引用"

#必需函数
def printme(str):
    "打印任何传入的字符串"
    print(str)
    return
# 调用 printme 函数，不加参数会报错
printme(str)

#关键字参数
def printme(str):
    "打印任何传入的字符串"
    print(str)
    return
#调用printme函数
printme(str = "菜鸟教程")

def printinfo(name,age):
    print("名字：",name)
    print("年龄：",age)
    return
#调用printinfo函数
printinfo(age=50,name="runoob")

#不定长参数
def printinfo(arg1,*var):
    print("输出：")
    print(arg1)
    print(var)
printinfo(70,60,50)

#如果在函数调用时没有指定参数，他就是一个空元组，我们也可以不向函数传递未命名的变量
def printinfo(arg2,*vartuple):
    print("输出：")
    print(arg2) 
    for var1 in vartuple:
        print(var1)
    return
printinfo(10)
printinfo(70,60,50)

#匿名函数 用lambda
sum = lambda arg1,arg2:arg1 + arg2
print ("相加后的值为：",sum(10,20))
print ("相加后的值为：",sum(30,20))

#return语句
def sum(arg1,arg2):
    #返回2个参数的和
    total = arg1 + arg2
    print("函数内：",total)
    return total
#调用sum函数
total =sum(10,20)
print("函数外：",total)
#初始化列表
tet_list=[1,5,6,9,4,7,8]
print("查看 4 是否在列表中 ( 使用循环 ) : ")

for i in tet_list:
    if(i==5):
        print("存在")
print("查看 4 是否在列表中 ( 使用 in 关键字 ) : ")
if (4 in tet_list):
    print("存在")

#清空
runoob =[5,2,6,8]
print('清空前：',runoob)
runoob.clear()
print('清空后：',runoob)

#移除列表中重复的元素
'''python集合：集合(set)是一个无序的不重复序列
   pyhthon列表：列表是一种数据项结构的有限序列，
   即按照一定的线性顺序排列而成的数据项集合，
   在这种数据结构上进行的基本操作包括对元素的查找，插入，删除
'''
list_1=[1,2,1,4,6]
print(list(set(list_1)))
list_2=[7,8,2,1]
print(list(set(list_1) ^ set(list_2)))
#"^"运算符得到两个列表的对称差（排除连个集合的重叠元素）

#复制列表
def clone_runoob(li1):
    li_copy = li1[:]
    return li_copy
li1=[4,8,2,10,15,18]
li2=clone_runoob(li1)
print("原始列表：",li1)
print("复制后的列表：",li2)

#使用extend()方法
def clone_runoob(li1): 
    li_copy = [] 
    li_copy.extend(li1) 
    return li_copy 
  
li1 = [2,3,4,5,6,7,8] 
li2 = clone_runoob(li1) 
print("原始列表:", li1) 
print("复制后列表:", li2)

#使用listd()方法
def clone_runoob(li1): 
    li_copy = list(li1) 
    return li_copy 
  
li1 = [4, 8, 2, 10, 15, 18] 
li2 = clone_runoob(li1) 
print("原始列表:", li1) 
print("复制后列表:", li2)
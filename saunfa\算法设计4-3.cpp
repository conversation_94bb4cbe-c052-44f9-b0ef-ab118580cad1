#include <iostream>
#include <vector>
#include <algorithm>
using namespace std;

// 函数来找到数列的第n项
int findNthTerm(int n) {
    if (n == 1) return 1;
    if (n == 2) return 2;

    vector<int> a(n + 1);
    a[1] = 1;
    a[2] = 2;
    for (int i = 3; i <= n; i++) {
        a[i] = a[i - 1] + a[i - 2];
    }
    return a[n];
}

// 函数来找到前n项的最大值
int findMaxValue(int n) {
    vector<int> a(n + 1);
    a[1] = 1;
    a[2] = 2;
    int maxVal = 2;
    for (int i = 3; i <= n; i++) {
        a[i] = a[i - 1] + a[i - 2];
        maxVal = max(maxVal, a[i]);
    }
    return maxVal;
}

int main() {
    int n;
    cout << "请输入项数 n: ";
    cin >> n;

    int nthTerm = findNthTerm(n);
    int maxValue = findMaxValue(n);

    cout << "数列的第 " << n << " 项是: " << nthTerm << endl;
    cout << "前 " << n << " 项的最大值是: " << maxValue << endl;

    return 0;
}
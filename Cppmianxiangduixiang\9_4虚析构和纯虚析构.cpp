#include <iostream>
using namespace std;

class BaseA{
    public:
    BaseA(){}
    ~BaseA(){
        cout<<"SonA 销毁了"<<endl;
    }
};

class SonA:public BaseA{
    public:
    SonA():m_Value(NULL){
        m_Value = new int(10);
    }
    ~SonA(){
        cout<<"SonA 销毁了"<<endl;
        delete m_Value;
    }
    int* m_Value;
};


class BaseB{
    public:
    BaseB(){}
    ~BaseB(){
        cout<<"SonB 销毁了"<<endl;
    }
};
class SonB:public BaseB{
    public:
    SonB():m_Value(NULL){
        m_Value = new int(10);
    }
    virtual~SonB(){
        cout<<"SonB 销毁了"<<endl;
        delete m_Value;
    }
    int* m_Value;
};

int main(){
    BaseA* a = new SonA();
    delete a;

    BaseB* B = new SonB();
    delete B;

    //BaseB x;抽象类无法进行实例化

    return 0;
}
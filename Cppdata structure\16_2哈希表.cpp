#include <iostream>
using namespace std;

template<typename KeyType, typename ValueType>
class HasNode{
    public:
        KeyType key;
        ValueType value;
        HasNode *next;

        HasNode(const KeyType &key, const ValueType &value){
            this->key = key;
            this->value = value;
            this->next = NULL;
        }

};

template<typename KeyType, typename ValueType>
class HasTable{
    private:
        int size;
        HasNode<KeyType, ValueType> **table;

        int hash(const KeyType &key) const{
            int haskey = key % size;
            if(haskey < 0){
                haskey += size;
            }
            return haskey;
        }
    public:
        HasTable(int size=256);
        ~HasTable();
        void insert(const KeyType &key, const ValueType &value);
        void remove(const KeyType &key);
        bool find(const KeyType &key, ValueType &value) const;
};

template<typename KeyType, typename ValueType>
HasTable<KeyType, ValueType>::HasTable(int size){
    this->size = size;
    this->table = new HasNode<KeyType,ValueType> *[size];
    for(int i=0; i<size; ++i){
        this->table[i] = NULL;
    }
}

template<typename KeyType, typename ValueType>
HasTable<KeyType, ValueType>::~HasTable(){
    for(int i=0; i<size; ++i){
        if(table[i]){
            HasNode<KeyType,ValueType> *current = table[i];
            while(current){
                HasNode<KeyType, ValueType> *next = current->next;
                delete current;
                current = next;
            }
            table[i]= NULL;
        } 
    }
    delete table;
    table = NULL;
}

template<typename KeyType, typename ValueType>
void HasTable<KeyType, ValueType>::insert(const KeyType &key, const ValueType &value){
    int index = hash(key);
    HasNode<KeyType,ValueType> *now = new HasNode<KeyType,ValueType>(key,value);
    if(table[index] == NULL){
        table[index] = now;
    }else{
        now->next = table[index];
        table[index] = now;
    }
}

template<typename KeyType, typename ValueType>
void HasTable<KeyType, ValueType>::remove(const KeyType &key){
    int index = has(key);
    if(table[index]){
        if(table[index]->key == key){
            HasNode<KeyType,ValueType> *next = table[index]->next;
            delete table[index];
            table[index] = next;
        }else{
            HasNode<KeyType, ValueType> *current = table[index];
            while(current->next && current->next->key !=key){
                current = current->next;
            }
            if(current->next){
                HasNode<KeyType,ValueType>*next = current->next->next;
                delete current->next;
                current->next = next;
            }
        }
    }
}
template<typename KeyType, typename ValueType>
bool HasTable<KeyType, ValueType>::find(const KeyType &key, ValueType &value) const{
    int index = hash(key);
    if(table[index]){
        if(table[index]->key ==key){
            value = table[index]->value;
            return true;
        }else{
            HasNode<KeyType, ValueType> *current = table[index];
            while(current->next && current->next->key !=key){
                current = current->next;
            }
            if(current->next){
                value = current->next->value;
                return true;
            }
        }     
    }
    return false;
}

int main(){
    HasTable<int, char> h(1000);
    h.insert(1, 'a');
    h.insert(2, 'b');
    h.insert(3, 'c');
    h.insert(41012012, 'd');

    char val;
    if(!h.find(43, val)){
        cout<<"43 not found!"<<endl;
    }
    if(h.find(41012012, val)){
        cout<<"41012012 found, value is"<<val<<endl;
    }
}
#include <iostream>
#include <vector>
#include <algorithm>
using namespace std;


// 递归函数来生成排列
void permute(vector<int>& nums, int start, vector<vector<int>>& result) {
    if (start >= nums.size()) {
        result.push_back(nums);
        return;
    }
    for (int i = start; i < nums.size(); ++i) {
        // 交换当前元素和起始位置的元素
        swap(nums[start], nums[i]);
        // 递归生成排列
        permute(nums, start + 1, result);
        // 回溯，将当前元素换回原来的位置
        swap(nums[start], nums[i]);
    }
}

// 函数用于初始化并调用递归函数
vector<vector<int>> generatePermutations(int n) {
    vector<int> nums;
    vector<vector<int>> result;
    
    // 初始化数组
    for (int i = 0; i < n; ++i) {
        nums.push_back(i + 1);
    }
    
    // 调用递归函数
    permute(nums, 0, result);
    
    return result;
}

// 主函数，用于展示结果
int main() {
    int n;
    cout << "请输入元素的数量： ";
    cin >> n;
    
    vector<vector<int>> permutations = generatePermutations(n);
    
    cout << "Permutations:\n";
    for (const auto& perm : permutations) {
        for (int num : perm) {
            cout << num << " ";
        }
        cout << "\n";
    }
    
    return 0;
}
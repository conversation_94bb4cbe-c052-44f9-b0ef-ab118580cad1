#include <iostream>
#include <vector>
#include <algorithm> // 包含std::reverse

void leftRotate(std::vector<char>& A, int n, int k) {
    if (n == 0 || k == 0 || k % n == 0) return; // 如果数组为空，或者k为0，或者k是n的倍数，则不需要旋转
    k = k % n; // 防止k大于数组长度
    std::reverse(A.begin(), A.begin() + k); // 反转前k个元素
    std::reverse(A.begin() + k, A.end()); // 反转剩余的元素
    std::reverse(A.begin(), A.end()); // 反转整个数组
}

int main() {
    std::vector<char> A = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'};
    int n = A.size();
    int k = 3;
    leftRotate(A, n, k);
    for (char c : A) {
        std::cout << c;
    }
    return 0;
}
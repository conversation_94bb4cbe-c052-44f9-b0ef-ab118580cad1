#include <iostream>
#include <string>
#include <vector>
#include <algorithm>

using namespace std;

// 函数：判断大整数能否被11整除
bool isDivisibleBy11(const string& number) {
    // 从右端开始，每两位一组分割整数
    vector<int> groups;
    for (int i = number.length() - 2; i >= 0; i -= 2) {
        // 从右端开始读取两位数
        int group = stoi(number.substr(i, 2));
        groups.push_back(group);
    }
    // 如果长度为奇数，添加最左边的一位数
    if (number.length() % 2 != 0) {
        groups.push_back(number[0] - '0');
    }

    // 求和
    int sum = 0;
    for (int group : groups) {
        sum += group;
    }

    // 判断和能否被11整除
    return sum % 11 == 0;
}

int main() {
    string number;
    cout << "请输入一个大整数：";
    cin >> number;

    if (isDivisibleBy11(number)) {
        cout << "该数能被11整除。" << endl;
    } else {
        cout << "该数不能被11整除。" << endl;
    }

    return 0;
}
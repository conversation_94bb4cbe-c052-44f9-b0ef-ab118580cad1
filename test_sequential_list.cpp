#include <iostream>
using namespace std;

#define eleTpye int

//定义一个类
struct SequentialList{
    eleTpye *elements;
    int size; //定义顺序表长度
    int capacity; //定义顺序表容量
    //size <= capacity
};

//初始化
//传递的是一个指针
void initializeList(SequentialList* list, int capacity){
    list->elements  = new eleTpye[capacity];
    list->size = 0;//顺序表一开始为空
    list->capacity = capacity;
}

//顺序表的销毁
void destroyList(SequentialList* list){
    //通过关键字：delete 直接拿销毁
    delete [] list->elements;//内存空间被回收

}

//获取元素个数的接口，获取顺序表的大小的接口
int size(SequentialList* list){
    return list->size;
}

bool isEmpty(SequentialList* list){
    return list->size == 0;
}

//顺序表的插入
void insert(SequentialList* list, int index, eleTpye element){
    //1.判断index是否合法
    if(index<0 || index>list->size){
        throw std::invalid_argument("Invaid index"); 
    }
    if(list->size == list->capacity){
        //当size=capacity说明用完了，需要扩容
        int newCapacity = list->capacity * 2;
        eleTpye *newElements = new eleTpye[newCapacity];
        //把原来的元素赋值到新的元素上
        for(int i=0; i<list->size; ++i){
            newElements[i] = list->elements[i];
        }
        //把旧的list的element给删除
        delete[] list->elements;
        //扩容
        list->elements = newElements;
        list->capacity = newCapacity;
    }
    for(int i=list->size; i>index; i--){
        //把当前元素往后挪
        list->elements[i] = list->elements[i-1];
    }
    list->elements[index] = element;
    list->size ++;
}

//顺序表的元素删除
void deleteElement(SequentialList* list, int index){
    //1.判断index是否合法
    if(index<0 || index>=list->size){
        throw std::invalid_argument("Invaid index"); 
    }
    for(int i=index; i<list->size-1; ++i){
        list->elements[i] = list->elements[i+1];
    }
    list->size --;
}

//顺序表的元素查找
int findElement(SequentialList* list, eleTpye element){
    //循环作比较
    for(int i=0; i<list->size; ++i){
        if(list->elements[i] == element){
            return i;
        }
    }
    return -1;
}

//顺序表的元素索引
//获取第index个元素
eleTpye getElement(SequentialList* list, int index){
    //1.判断index是否合法
    if(index<0 || index>=list->size){
        throw std::invalid_argument("Invaid index"); 
    }
    return list->elements[index];
}

//顺序表的元素修改
void updateElement(SequentialList* list, int index, eleTpye value){
    //1.判断index是否合法
    if(index<0 || index>=list->size){
        throw std::invalid_argument("Invaid index"); 
    }
    list->elements[index] = value;
}

//验证
int main(){
    SequentialList myList;
    initializeList(&myList, 10);
    for(int i=0; i<10; ++i){
        insert(&myList, i, i*10);
    }
    cout<<"size:"<<size(&myList)<<endl;
    cout<<"is empty:"<<isEmpty(&myList)<<endl;

    for(int i=0; i<size(&myList); ++i){
        cout<<getElement(&myList,i)<<" ";
    }
    cout<<endl;
    //验证删除
    deleteElement(&myList, 5);
    updateElement(&myList, 1, 1314);
    for(int i=0; i<size(&myList); ++i){
        cout<<getElement(&myList,i)<<" ";
    }
    cout<<endl;
    //验证查找
    int idx = findElement(&myList, 20);
    updateElement(&myList ,idx, 520);
    for(int i=0; i<size(&myList); ++i){
        cout<<getElement(&myList,i)<<" ";
    }
    cout<<endl;

    destroyList(&myList);

    return 0;
}

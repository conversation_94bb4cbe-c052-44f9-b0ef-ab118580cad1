/*
问题描述
输入一个英文句子，将每个单词的第一个字母改成大写字母。
 
输入
输入数据包含多个测试实例，每个测试实例是一个长度不超过100的英文句子，占一行。
 
输出
请输出按照要求改写后的英文句子。
 
样本输入
i like acm
i want to get an accepted
 
示例输出
I Like Acm
I Want To Get An Accepted
*/

#include <iostream> // 包含输入输出流库，用于标准输入输出操作
#include <cstring>  // 包含字符串处理函数，如strlen等
using namespace std; // 使用标准命名空间，避免在调用标准库函数时需要加std::

int main(){ // 主函数，程序的入口
    char s[110]; // 定义一个字符数组s，大小为110，用于存储输入的字符串
    while(gets(s)){ // 使用gets函数循环读取用户输入的字符串，直到遇到文件结束符（EOF）
        int len = strlen(s); // 调用strlen函数获取字符串s的长度，并存储在变量len中
        for(int i=0; i<len; ++i){ // 使用for循环遍历字符串s中的每个字符
            if(i==0 || s[i-1] ==' '){ // 判断当前字符是否是字符串的第一个字符，或者前一个字符是否是空格
                if(s[i]!=' '){ // 判断当前字符是否不是空格
                    if(s[i] >= 'a' && s[i]<='z'){ // 判断当前字符是否是小写字母
                        s[i]-='a'; // 将当前字符减去'a'的ASCII值，得到其在字母表中的偏移量
                        s[i]+='A'; // 将偏移量加上'A'的ASCII值，将小写字母转换为大写字母
                    }
                }
            }
        }
        printf("%s\n",s); // 使用printf函数输出处理后的字符串，并换行
    }
    return 0; // 主函数返回0，表示程序正常结束
}
#include <iostream>  // 引入输入输出流库
#include <string>
#include <cstring>
#include <stdexcept> // 引入异常处理库

using namespace std; // 使用标准命名空间

// 定义一个模板类 Queue，用于实现队列
template<typename T>
class Queue {
private:
    struct Node {  // 定义节点结构体
        T data;    // 节点存储的数据
        Node *next; // 指向下一个节点的指针
        Node(T d) : data(d), next(NULL) {}  // 构造函数，初始化节点的数据和指针
    };

    Node *front; // 队首指针
    Node *rear;  // 队尾指针
    int size;    // 队列的大小

public:
    Queue() : front(NULL), rear(NULL), size(0) {}  // 构造函数，初始化队列为空
    ~Queue();  // 析构函数的声明

    void enqueue(T element);  // 入队操作的声明
    T dequeue();  // 出队操作的声明
    T getFront() const;  // 获取队首元素的声明
    int getSize() const;  // 获取队列大小的声明
};

// 析构函数的实现
template<typename T>
Queue<T>::~Queue() {
    while (front) {  // 当队列不为空时
        Node *temp = front;  // 保存当前队首节点
        front = front->next; // 将队首指针移动到下一个节点
        delete temp;  // 删除当前节点
    }
}

// 入队操作的实现
template<typename T>
void Queue<T>::enqueue(T element) {
    if (rear == NULL) {  // 如果队列为空
        rear = new Node(element);  // 创建新节点并设置为队尾
        front = rear;  // 同时设置为队首
    } else {
        rear->next = new Node(element);  // 在队尾添加新节点
        rear = rear->next;  // 更新队尾指针
    }
    ++size;  // 增加队列大小
}

// 出队操作的实现
template<typename T>
T Queue<T>::dequeue() {
    if (front == NULL) {  // 如果队列为空
        throw std::underflow_error("Queue is empty");  // 抛出异常
    }
    T element = front->data;  // 获取队首元素的数据
    Node *temp = front;  // 保存当前队首节点
    front = front->next;  // 将队首指针移动到下一个节点
    delete temp;  // 删除当前节点
    --size;  // 减少队列大小
    return element;  // 返回队首元素的数据
}

// 获取队首元素的实现
template<typename T>
T Queue<T>::getFront() const {
    if (front == NULL) {  // 如果队列为空
        throw std::underflow_error("Queue is empty");  // 抛出异常
    }
    return front->data;  // 返回队首元素的数据
}

// 获取队列大小的实现
template<typename T>
int Queue<T>::getSize() const {
    return size;  // 返回队列的大小
}

int team[1000000];
// 主函数
int main() {
    int t;
    int cases =0;
    while(cin>>t && t){
        ++cases;
        memset(team, 0, sizeof(team));
        for(int i=1; i<=t; ++i){
            int n;
            cin>>n;
            while(n--){
                int x;
                cin>>x;
                team[x] = i;
            }
        }
        Queue<int> q[1001];
        int s=0, e=0;
        string str;
        cout<<"Scensrio #"<<cases<<endl;
        while(cin>>str){
            if(str == "STOP")break;
            if(str == "ENQUEUE"){
                int x,i;
                cin>>x;
                for( int i=s; i<e; ++i){
                    if(team[q[i].getFront()] == team[x]) {
                        break;
                    }
                }
                q[i].enqueue(x);
                if(i == e){
                    ++e;
                }
            }else{
                int val = q[s].dequeue();
                if( q[s].getSize() == 0){
                    ++s;
                }  
                cout<<val<<endl;          
            }
        }
         cout<<endl;
    }
    return 0;
}